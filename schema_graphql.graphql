type Query {
  project(orgHandler: String, orgId: Int!, projectId: String!): Project
  projects(orgId: Int!, orgHandler: String): [Project!]
  projectCreationEligibility(orgId: Int!, orgHandler: String!): ProjectCreationEligibility
  projectHandlerAvailability(orgId: Int!, projectHandlerCandidate: String!): ProjectHandlerAvailability
  componentNameAvailability(projectId: String!, componentNameCandidate: String!, scope: String, orgHandler: String): ComponentNameAvailability
  component(orgHandler: String, componentId: String, projectId: String, componentHandler: String): Component
  components(orgHandler: String!, projectId: String, options: ComponentsOptions): [Component!]
  buildPipelineMetadata(componentId: String!): BuildPipelineMetadata
  projectComponentLabels(orgId: Int!, projectId: String!): [String!]
  lastCommit(orgHandler: String, componentId: String!): CommitInformation
  deploymentStatusByVersion(versionId: String!, componentId: String!, buildConclusion: String): [DeploymentStatusResp!]
  environments(orgHandler: String, orgUuid: String!, type: String, projectId: String, isOrgBasicInfo: Boolean): [Environment!]
  insightsEnvironments(orgUuid: String!, projectId: String): [InsightsEnvironment!]
  workflowStatusByName(workflowName: String!, componentId: String!): WorkflowStatusDataMapper
  commitHistory(orgHandler: String, componentId: String!, branch: String, versionId: String = ""): [CommitHistory!]
  tagHistory(orgHandler: String, componentId: String!): [TagHistory!]
  branchList(orgHandler: String!, componentId: String!): [Branch!]
  deployments(orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!): [DeployedEnvironmentData!]!
  subscriptionUsageStatus(orgUuid: String!): UsageStatus!
  deploymentsV2(orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!): [DeployedEnvironment!]!
  componentDeployment(orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!, environmentId: String!): ComponentDeployment
  proxyDeployment(orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!, environmentId: String!): ProxyDeployment
  deployment(environmentId: String!, orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!): DeployedEnvironmentData!
  invokeInformation(orgHandler: String!, orgUuid: String!, componentId: String!, componentType: String!, versionId: String!, projectId: String): [InvokeData!]
  invokeUrl(orgHandler: String!, orgUuid: String!, componentId: String!, componentType: String!, versionId: String!, envId: String!): String
  invokeUrls(orgHandler: String!, orgUuid: String!, componentId: String!, versionId: String!): [BasicInvokeData!]
  buildsByEnvironments(orgHandler: String!, build: BuildRevision!): [EnvBuild!]
  buildsByVersion(orgHandler: String!, build: BuildRevision!, skip: Int, limit: Int): [BuildMapping!]
  build(orgHandler: String!, build: BuildRevision!): BuildMapper
  revisionsByEnv(orgHandler: String!, build: BuildRevision!): BuildMapper
  userRepos(secretRef: String): [GitHubOrgsAndRepositoriesForUser!]
  userRepoValidation(organizationName: String!, repoName: String!): UserGitHubRepositoryValidationData
  isValidNonEmptyRepo(organizationName: String!, repoName: String!, branch: String!, subPath: String): UserGitHubRepositoryValidationData
  componentPullRequests(componentId: String!): [UserGitHubRepositoryPR!]
  systemStatus: SystemStatus
  userRepoStatus(componentId: String!): UserGitHubRepositoryStatusData
  repoDirectoryList(repositoryOrganization: String!, repositoryName: String!, repositoryBranch: String!): [RepositoryDirectory!]
  repoBranchList(repositoryOrganization: String!, repositoryName: String!, secretRef: String, isPublicRepo: Boolean): [RepositoryBranch!]
  containsFile(componentId: String!, commit: String!, path: String!): Boolean
  repositoryContents(input: GetRepositoryContentsEndpointInputs!): [RepositoryContent!]
  oasDefinition(componentId: String!, versionId: String!, environmentId: String!): String
  autoBuildTrigger(componentId: String!, versionId: String!): AutoDeploymentResMapper
  scanResult(componentId: String!, branch: String!): ScanResult
  buildLogs(componentId: String!, runId: String!): BuildLogs
  repoMetadata(organizationName: String!, repoName: String!, branch: String!, subPath: String, dockerFilePath: String, dockerContextPath: String, componentId: String, openAPIPath: String, libPath: String, secretRef: String, buildpackId: String, testRunnerType: String, isService: Boolean, isPublicRepo: Boolean, isGitProxy: Boolean): RepositoryMetaDataMapperData
  cellDiagram(componentId: String!, commitHash: String!): CellDiagram
  componentWebhook(componentId: String!): ComponentWebhook
  distinctComponentTypeCount(orgId: Int!): [DistinctComponentCount!]
  componentEndpoint(input: QueryComponentEndpointInput!): ComponentEndpoint
  componentEndpoints(input: QueryComponentEndpointsInput!): [ComponentEndpoint!]
  componentEndpointApiDefinitionByEndpointNameAndCommitHash(input: QueryComponentEndpointApiDefinitionByEndpointNameAndCommitHashInput!): ComponentEndpointApiDefinition
  componentEndpointApiDefinition(input: QueryComponentEndpointApiDefinitionInput!): ComponentEndpointApiDefinition
  commonCredentials(orgUuid: String!, projectId: String, scope: String, type: String): [CommonCredentialService!]
  commonCredential(orgUuid: String!, projectId: String, credentialId: String!): CommonCredentialService
  checkDeleteCommonCredentialV2(orgUuid: String!, credentialId: String!): CheckDeleteGitCredentialResp!
  gitTokenPermissions(input: QueryGitTokenPermissions!): GitPermissions
  deploymentTrackImages(input: GetDeploymentTrackImagesInput!): [DeploymentTrackImage!]
  migratedSourceConfigurationContent(input: GetMigratedSourceConfigurationContentInput!): MigratedSourceConfigurationContent
  componentPaths(orgUuid: String!, organizationName: String!, repoName: String!, branch: String!, secretRef: String): [ComponentPath!]
  execution(input: QueryExecutionInput!): Execution
  executions(input: QueryExecutionsInput!): [Execution!]
  executionConfigs(componentId: String!, releaseId: String!): ExecutionConfigs
  userProvidedEndpoints(componentId: String!, deploymentTrackId: String!): [UserProvidedEndpoint!]
  organizationGovernancePolicies: organizationGovernancePolicies
  gitTokenForRepository(req: GitTokenRequest!): GitToken
}

type Project {
  name: String
  handler: String
  region: String
  deploymentPipelineId: String
  deploymentPipelineIds: [String!]!
  defaultDeploymentPipelineId: String
  extendedHandler: String
  version: String
  labels: String
  orgId: Int
  id: String
  owner: String
  description: String
  createdDate: String
  updatedAt: String
  repository: String
  gitOrganization: String
  branch: String
  directoryPath: String
  type: String
  secretRef: String
  gitProvider: String
  components(orgHandler: String!): [Component!]
  gitRepositoryUrl: String
  projectRepoReadmeContent: String
  projectContributorsData: ProjectContributorData!
  allowedPermissions: [ProjectPermission]
}

type ProjectPermission {
  key: String!
  value: [String!]!
}

"Component"
type Component {
  name: String
  status: String
  version: String
  labels: [String!]
  project: Project
  description: String
  ownerName: String
  orgId: Int
  orgUuid: String
  handler: String
  orgHandler: String
  canDelete: Boolean
  createStatus: String
  repository: RepositoryResponseDataMapper
  projectId: String
  apiId: String
  deleteReason: String
  displayName: String
  displayType: String
  createdAt: String
  lastBuildDate: String
  updatedAt: String
  id: String
  apiVersions: [ApiVersionEntry!]
  deploymentTracks: [DeploymentTrack!]
  httpBased: Boolean
  initStatus: String
  isMigrationCompleted: Boolean
  skipDeploy: Boolean
  isUnifiedConfigMapping: Boolean!
  endpointShortUrlEnabled: Boolean!
  serviceAccessMode: String
  componentSubType: String
  originCloud: String
  isSystemComponent: String
}

"RepositoryResponseDataMapper"
type RepositoryResponseDataMapper {
  "Id"
  id: String!
  "Code repository name"
  nameApp: String!
  "Config repository name"
  nameConfig: String!
  "Code organization name"
  organizationApp: String!
  "Config organization name"
  organizationConfig: String!
  "Branch"
  branch: String!
  "Branch for source repo"
  branchApp: String!
  "Code repository reference"
  repoCredRef: String!
  "Config repository reference"
  gitOpsCredRef: String!
  "Organization Id"
  orgId: Int!
  "Project Id"
  projectId: String!
  "Component Id"
  componentId: String!
  "User managed git repositoty or not"
  isUserManage: Boolean!
  "User manager repository PR information"
  managedPRs: [UserGitHubRepositoryPR!]
  "sub path of the repository"
  appSubPath: String
  "Container build configs"
  byocBuildConfig: ByocBuildConfig
  "Web app build configs"
  byocWebAppBuildConfig: ByocWebAppBuildConfig
  "Test runner component build configs"
  testRunnerConfig: ByocDockerfileLessTestRunnerConfigResp
  buildpackConfig: [BuildpackBuildConfig!]
  "Field to identify the git provider where the source repo is hosted"
  gitProvider: String
  "Bitbucket server url"
  bitbucketServerUrl: String
  "Server url for any git vendor if it is privately hosted"
  serverUrl: String
  isAuthorizedRepo: Boolean
}

"`UserGitHubRepositoryPR`"
type UserGitHubRepositoryPR {
  "GitHub PR url"
  url: String!
  "GitHub PR number"
  number: Int!
  "state of the PR; either open or close."
  state: String!
}

"ByocBuildConfig"
type ByocBuildConfig {
  "Id"
  id: String!
  "Repository id"
  repositoryId: String!
  "Container Id"
  containerId: String!
  "Component Id"
  componentId: String!
  "Docker file path"
  dockerfilePath: String!
  "Docker context"
  dockerContext: String!
  "Main container falg"
  isMainContainer: Boolean!
  "OAS file path"
  oasFilePath: String
}

"BYOC Web App Build Config"
type ByocWebAppBuildConfig {
  "Id"
  id: String!
  "Repository id"
  repositoryId: String!
  "Container Id"
  containerId: String!
  "Component Id"
  componentId: String!
  "Docker context"
  dockerContext: String
  "Web app type"
  webAppType: String!
  "Build command"
  buildCommand: String
  "Package manager version"
  packageManagerVersion: String
  "Output directory"
  outputDirectory: String!
  "Enable trivy scan"
  enableTrivyScan: Boolean
}

"""
`ByocDockerfileLessTestRunnerConfigResp` contains configuration
related to BYOC DockerfileLess test runner component.
"""
type ByocDockerfileLessTestRunnerConfigResp {
  "Docker context"
  dockerContext: String
  "Postman collection directory"
  postmanDirectory: String
  "Test runner component type"
  testRunnerType: String!
}

"BuildpackBuildConfig"
type BuildpackBuildConfig {
  "Build Context"
  buildContext: String!
  "Language Version"
  languageVersion: String
  "Version Id"
  versionId: String!
  "Buildpack"
  buildpack: Buildpack!
  "Build Environment key value pairs"
  keyValues: [BuildEnvironmentVariableKeyValue!]
  "Unit test enabled/disabled state"
  isUnitTestEnabled: Boolean
  "Pull latest submodules"
  pullLatestSubmodules: Boolean
  "Enable container trivy scan"
  enableTrivyScan: Boolean
  "Run command"
  runCommand: String
}

"Buildpack"
type Buildpack {
  "Buildpack Id"
  id: String!
  "Buildpack Image"
  buildpackImage: String
  "Display Name"
  displayName: String!
  "Supported Versions"
  supportedVersions: String!
  "Language"
  language: String!
  "Icon Url"
  iconUrl: String
  "Provider"
  provider: String
}

"Represent the Buildpack Build environment variable key-value pair"
type BuildEnvironmentVariableKeyValue {
  id: String!
  key: String!
  value: String!
}

type ApiVersionEntry {
  id: String
  createdAt: String
  updatedAt: String
  organizationId: String
  projectId: String
  apiVersion: String
  branch: String
  versionId: String
  appId: String
  latest: Boolean
  accessibility: String
  proxyName: String
  proxyUrl: String
  proxyId: String
  state: String
  appEnvVersions: [AppEnvVersion!]
  autoDeployEnabled: Boolean!
  cellDiagram: CellDiagram
}

type AppEnvVersion {
  environmentId: String!
  releaseId: String!
  release: AppEnvironment!
}

type AppEnvironment {
  id: String!
  createdAt: String!
  updatedAt: String!
  environmentId: String
  environment: String
  gitHash: String
  gitOpsHash: String
  imageId: String
  lastTriggeredAt: String
  cronJobFrequency: String!
  cronJobTimezone: String
  deploymentStatus: String!
  unDeployed: Boolean!
  containers: [Container!]
  metadata: EnvironmentMetadata!
  webAppUrl: String
}

type Container {
  id: String!
  createdAt: String!
  updatedAt: String!
  image: Image
  metadata: ContainerMetadata
}

type Image {
  id: String!
  createdAt: String!
  ipdatedAt: String!
  imageVersion: String
  imageRegistryId: String
  imageRegistry: String
  tagName: String!
  imageName: String!
  imageNameWithTag: String!
  status: String!
  tags: [String!]!
  platformerTag: String!
  gitHash: String!
  committer: String
  commitMsg: String!
  buildPipelineRunId: String
}

type ContainerMetadata {
  buildPipelineRunId: String
}

type EnvironmentMetadata {
  choreoEnv: String!
}

"CellDiagram service class provides the methods to get the cell diagram."
type CellDiagram {
  data: String
  message: String
  errorName: String
  success: Boolean
}

"Deployment Track object that is exposed from the GraphQL"
type DeploymentTrack {
  "Unique UUID of the deployment track object."
  id: String!
  "Created date for the deployment track"
  createdAt: String!
  "Updated date for the deployment track"
  updatedAt: String!
  "Optional field that indicate the API version of this deployment track."
  apiVersion: String
  "Optional field that indicates the branch associated with this deployment track."
  branch: String
  "Optional field that store the description of this deployment track."
  description: String
  "Component id of the deployment track."
  componentId: String!
  "Indicates whether this deployment track is the latest deployment track."
  latest: Boolean!
  """
  Optional field storing API versioning strategy for this deployment track. 
  Values: Major, MajorMinor, LegacyMajorMinorPatch or empty if the component does not expose an API.
  """
  versionStrategy: String
  autoDeployEnabled: Boolean!
}

"Represent project contributor data"
type ProjectContributorData {
  "total contributor count"
  contributorCount: Int!
  "contributor count"
  contributors: [ContributorData!]!
}

"Represents Contributor user data"
type ContributorData {
  "user id"
  id: Int!
  "user picture url"
  pictureUrl: String!
  "user email"
  email: String!
  "user display name"
  displayName: String!
  "total contributions"
  totalContributions: Int!
}

"Project creation eligibility record"
type ProjectCreationEligibility {
  "Whether the project creation is allowed"
  isProjectCreationAllowed: Boolean!
}

"Project Handler Availability record"
type ProjectHandlerAvailability {
  handlerUnique: Boolean!
  alternateHandlerCandidate: String
}

"Component Name Availability record"
type ComponentNameAvailability {
  componentNameUnique: Boolean!
  alternateComponentName: String
}

type Mutation {
  createProject(project: ProjectSchema!): Project
  createProjectRepository(project: ProjectSchema!, projectId: String!): Project
  updateProject(project: ProjectSchema!): Project
  deleteProject(orgHandler: String, orgId: Int!, projectId: String!): DeleteProject
  deleteComponent(orgHandler: String!, componentId: String!, projectId: String!): Component
  deleteComponentV2(orgHandler: String!, componentId: String!, projectId: String!, skipValidation: Boolean): DeleteComponent
  createComponent(component: ComponentSchema!): Component
  createByocComponent(component: ByocCreateComponentSchema!): ByocComponent
  createBuildpackComponent(component: ByocCreateComponentSchema!): ByocComponent
  createMcpComponent(component: McpCreateComponentSchema!): McpComponent
  createByoiComponent(component: ByoiCreateComponentSchema!): ByoiComponent
  createIntegrationComponent(component: IntegrationCreateComponentSchema!): IntegrationComponent
  createProjectComponent(project: ProjectSchema!, component: ComponentSchema!): Component
  updateComponent(component: ComponentUpdateSchema!): Component
  rerunCreateWorkflow(runId: String!, componentId: String!): RerunCreateResMapper
  promote(orgHandler: String, componentId: String!, promoteSchema: Promote!): String
  updateCronFrequency(orgHandler: String, componentId: String!, environmentId: String!, versionId: String!, cronFrequency: String!, cronTimezone: String): String @deprecated
  updateJobConfigs(input: JobConfigInput!): String
  createVersion(orgHandler: String!, orgUuid: String!, componentId: String!, version: String!, componentType: String, apiId: String, branch: String, baseApiVersionId: String, skipBranchCheck: Boolean): Version
  deployComponent(deployment: Deployment!): TriggerDeploymentResMapper
  cancelBuild(input: CancelBuildInput!): String!
  redeployDeployment(orgHandler: String!, componentId: String!, releaseId: String!, type: String!, applyConfigUpdates: Boolean! = false): String
  stopDeployment(orgHandler: String!, componentId: String!, releaseId: String!, type: String!, clearCron: Boolean! = false): String
  createBuild(orgHandler: String!, build: BuildRevision!): BuildMapper
  createBuildRevision(orgHandler: String!, build: BuildRevision!): BuildMapper
  deleteBuild(orgHandler: String!, build: BuildRevision!): BuildMapper
  createRevision(orgHandler: String!, build: BuildRevision!): BuildMapper
  deleteRevision(orgHandler: String!, build: BuildRevision!): BuildMapper
  deleteRevisionEnvironment(orgHandler: String!, build: BuildRevision!): BuildMapper
  obtainUserToken(authorizationCode: String!): ObtainUserTokenResponseData
  cancelDeploymentRun(componentId: String!, versionId: String!): CancelDeploymentResMapper
  updateApiAccessibility(componentId: String!, versionId: String!, accessibility: String!): ApiVersion
  handleEnableAutoBuild(componentId: String!, versionId: String!, envId: String!): AutoDeploymentStatus
  handleConfigInit(componentId: String!): ConfigInitStatus
  handleDisableAutoBuild(componentId: String!, versionId: String!, envId: String!): AutoDeploymentStatus
  updateByocBuildConfigurations(componentId: String!, dockerContext: String, dockerFilePath: String, oasFilePath: String): String
  updateBuildpackBuildConfigurations(input: UpdateBuildpackBuildConfigInput!): String
  updateWebAppConfiguration(input: UpdateWebAppConfigInput!): String
  updateOasFileConfigurations(componentId: String!, oasFilePath: String!): String
  createComponentEndpoint(input: CreateComponentEndpointInput!): ComponentEndpoint
  updateComponentEndpoint(input: UpdateComponentEndpointInput!): ComponentEndpoint
  deleteComponentEndpoint(input: DeleteComponentEndpointInput!): ComponentEndpoint
  promoteComponentEndpoints(input: PromoteComponentEndpointInput!): [ComponentEndpoint!]
  generateComponentEndpoints(input: GenerateComponentEndpointInput!): [ComponentEndpoint!]
  reassignEndpointShortUrl(input: ReassignEndpointShortUrlInput!): [ComponentEndpoint!]
  deployImage(input: DeployImageInput!): TriggerDeploymentResMapper
  createCommonCredential(credential: CreateCommonCredentialInput!): CommonCredentialService
  deleteCommonCredential(orgUuid: String!, projectId: String, credentialId: String!): String
  deleteCommonCredentialV2(orgUuid: String!, credentialId: String!): DeleteCommonCredentialResp
  updateCommonCredential(projectId: String, credentialId: String!, credential: UpdateCommonCredentialInput!): CommonCredentialService
  createDeploymentTrack(input: CreateDeploymentTrackInput!): DeploymentTrack
  updateDeploymentTrack(input: UpdateDeploymentTrackInput!): DeploymentTrack
  deployDeploymentTrack(input: DeployDeploymentTrackInput!): String
  deleteDeploymentTrack(input: DeleteDeploymentTrackInput!): DeleteDeploymentTrackRes
  checkDeploymentTrackDeletable(input: DeleteDeploymentTrackInput!): CheckDeploymentTrackDeletableRes
  updateTestRunnerConfiguration(input: UpdateTestRunnerConfigInput!): String
  terminateExecution(input: TerminateExecutionInput!): String
  updateUserProvidedEndpoint(input: UpdateUserProvidedEndpointInput!): UserProvidedEndpoint
  updateOrganizationGovernancePolicies(input: UpdateOrganizationSettingsInput!): OrganizationSettings
  updateOrganizationSecretAccessPolicy(input: UpdateOrganizationSecretAccessPolicyInput!): UpdateOrganizationSecretAccessPolicyResponse
  createProxyComponent(component: ProxyCreateComponentSchema!): ComponentData
  triggerProxyBuild(deployment: Deployment!): TriggerDeploymentResMapper
  createCodeServer(codeServer: CodeServerInput!): String
  updateCodeServer(input: CodeServerInput!): String
}

"ProjectSchema"
input ProjectSchema {
  name: String!
  projectHandler: String
  description: String!
  version: String!
  orgId: Int!
  orgHandler: String
  repository: String
  branch: String
  secretRef: String
  gitProvider: String
  region: String
  id: String
  deploymentPipelineId: String
  gitOrganization: String
  directoryPath: String
  isPublicRepo: Boolean
  defaultDeploymentPipelineId: String
}

"DeleteProject"
type DeleteProject {
  "Deleted status"
  status: String!
  "Whether the project can be deleted"
  details: String!
}

"ComponentsOptions"
input ComponentsOptions {
  filter: ComponentsFilterOptions
}

"ComponentsFilterOptions"
input ComponentsFilterOptions {
  type: String
  types: [String!]
  limit: Int! = ""
  offset: Int! = ""
  withSystemComponents: Boolean! = ""
}

"DeleteComponent"
type DeleteComponent {
  "Whether the component can be deleted"
  canDelete: Boolean!
  "Deleted status"
  status: String!
  "Human readable message indicating the reason for any failures"
  message: String!
  "Encoded dynamic data coming from the delete manager"
  encodedData: String
}

"ComponentSchema"
input ComponentSchema {
  name: String!
  orgId: Int!
  orgHandler: String!
  displayName: String!
  displayType: String!
  projectId: String!
  labels: String!
  version: String!
  description: String!
  accessibility: String
  ballerinaVersion: String
  ballerinaTemplate: String
  apiId: String
  sampleTemplate: String
  id: String
  httpBase: Boolean
  triggerID: Int
  triggerChannels: String
  srcGitRepoUrl: String
  repositorySubPath: String
  repositoryType: String
  repositoryBranch: String
  initializeAsBallerinaProject: Boolean
  enableCellDiagram: Boolean
  secretRef: String
  isAsyncCreation: Boolean
  isPublicRepo: Boolean
  skipCheckForMonorepo: Boolean
  serviceAccessMode: String
  componentSubType: String
  originCloud: String
  enableAutoDeploy: Boolean
  enableAutoBuild: Boolean
}

"ByocComponent"
type ByocComponent {
  "Component Id"
  id: String!
  "Created Timestamp"
  createdAt: String!
  "Updated Timestamp"
  updatedAt: String!
  "Component Name"
  name: String!
  "Organization Id"
  organizationId: String!
  "Project Id"
  projectId: String!
  "Organziation handler"
  orgHandle: String!
  type: String!
  "Description"
  description: String!
  "Image Registry Id"
  imageRegistryId: String!
  "Image Registry"
  imageRegistry: ImageRegistry!
  "Component type"
  componentType: String!
  "Is http based"
  httpBased: Boolean!
  handle: String!
}

"ImageRegistry"
type ImageRegistry {
  "id of the image registry"
  id: String!
  "created at timestamp"
  createdAt: String!
  "updated at timestamp"
  updatedAt: String!
  "cloud connector id"
  cloudConnectorId: String!
  "image repository name"
  imageRepositoryName: String!
}

"ByocCreateComponentSchema"
input ByocCreateComponentSchema {
  name: String!
  orgId: Int!
  orgHandler: String!
  projectId: String!
  labels: String!
  description: String!
  componentType: String!
  userId: Int
  byocConfig: ByocConfig
  byocWebAppsConfig: ByocDockerLessWebAppsConfig
  buildpackConfig: BuildpackConfig
  port: Int
  oasFilePath: String
  accessibility: String
  displayName: String!
  secretRef: String
  testRunnerConfig: ByocDockerfileLessTestRunnerConfig
  isAsyncCreationEnabled: Boolean
  isPublicRepo: Boolean
  enableAutoDeploy: Boolean
  enableAutoBuild: Boolean
  componentSubType: String
  originCloud: String
  userProvidedEndpointCreateData: [UserProvidedEndpointCreateData!]
}

"ByocConfig"
input ByocConfig {
  dockerfilePath: String!
  dockerContext: String!
  srcGitRepoUrl: String
  srcGitRepoBranch: String
}

"ByocDockerLessWebAppsConfig"
input ByocDockerLessWebAppsConfig {
  dockerContext: String!
  srcGitRepoUrl: String
  srcGitRepoBranch: String
  webAppType: String!
  webAppBuildCommand: String!
  webAppPackageManagerVersion: String!
  webAppOutputDirectory: String!
  isAppGatewayEnabled: Boolean
}

"BuildpackConfig"
input BuildpackConfig {
  buildContext: String!
  languageVersion: String
  buildpackId: String!
  srcGitRepoUrl: String
  srcGitRepoBranch: String
  envValues: [EnvVariableValue!]
  runCommand: String
}

input EnvVariableValue {
  envId: String!
  value: String
}

"Byoc Dockerfileless Test Runner Configuration type"
input ByocDockerfileLessTestRunnerConfig {
  srcGitRepoUrl: String
  srcGitRepoBranch: String
  testRunnerType: String
  postmanDirectory: String
  dockerContext: String
}

"Description."
input UserProvidedEndpointCreateData {
  port: Int
  type: String
  basePath: String
  schemaFilePath: String
  name: String
  displayName: String
}

"McpComponent"
type McpComponent {
  "Component Id"
  id: String!
  "Created Timestamp"
  createdAt: String!
  "Updated Timestamp"
  updatedAt: String!
  "Component Name"
  name: String!
  "Organization Id"
  organizationId: String!
  "Project Id"
  projectId: String!
  "Organziation handler"
  orgHandle: String!
  type: String!
  "Description"
  description: String!
  "Image Registry Id"
  imageRegistryId: String!
  "Image Registry"
  imageRegistry: ImageRegistry!
  "Component type"
  componentType: String!
  handle: String!
}

"McpCreateComponentSchema"
input McpCreateComponentSchema {
  name: String!
  displayName: String!
  orgId: Int!
  orgHandler: String!
  projectId: String!
  labels: String!
  description: String!
  componentType: String!
  serverType: String!
  runCommand: String!
  buildpackId: String!
  userId: Int
}

"ByoiComponent"
type ByoiComponent {
  "Component Id"
  id: String!
  "Created Timestamp"
  createdAt: String!
  "Updated Timestamp"
  updatedAt: String!
  "Component Name"
  name: String!
  "Project Id"
  projectId: String!
  "Organziation handler"
  orgHandle: String!
  orgUuid: String!
  type: String!
  "Description"
  description: String!
  "Image Registry Id"
  imageRegistryId: String!
  "Component type"
  componentType: String!
  handle: String!
}

"ByoiCreateComponentSchema"
input ByoiCreateComponentSchema {
  projectId: String!
  name: String!
  displayName: String!
  description: String!
  componentType: String!
  metadata: ComponentMetadata!
  port: Int
  imageRepo: String
  imageName: String
  imageTag: String
  imageUrl: String
  registryId: String!
}

"ComponentMetadata"
input ComponentMetadata {
  labels: String!
  version: String!
  componentSubType: String
  originCloud: String
  isSystemComponent: String
}

"IntegrationComponent"
type IntegrationComponent {
  "Component Id"
  id: String!
  "Created Timestamp"
  createdAt: String!
  "Updated Timestamp"
  updatedAt: String!
  "Component Name"
  name: String!
  "Organization Id"
  organizationId: String!
  "Project Id"
  projectId: String!
  "Organziation handler"
  orgHandle: String!
  type: String!
  "Description"
  description: String!
  "Image Registry Id"
  imageRegistryId: String!
  "Image Registry"
  imageRegistry: ImageRegistry!
  "Component type"
  componentType: String!
  "Is http based"
  httpBased: Boolean!
  handle: String!
}

"IntegrationCreateComponentSchema"
input IntegrationCreateComponentSchema {
  name: String!
  orgId: Int!
  orgHandler: String!
  projectId: String!
  labels: String!
  description: String!
  componentType: String!
  userId: Int
  srcGitRepoUrl: String
  srcGitRepoBranch: String
  port: Int
  accessibility: String
  displayName: String!
  version: String!
  oasFilePath: String
  repositorySubPath: String
  libSubPath: String
  secretRef: String
  isPublicRepo: Boolean
  enableAutoDeploy: Boolean
  enableAutoBuild: Boolean
  componentSubType: String
  originCloud: String
}

"BuildPipelineMetadata"
type BuildPipelineMetadata {
  "Workflow type"
  workflowType: String!
  "Cluster id"
  clusterId: String!
  "Organization type"
  orgType: String!
  "Whether workflow is enabled"
  hasWorkflowEnabled: Boolean!
}

"ComponentUpdateSchema"
input ComponentUpdateSchema {
  displayName: String!
  labels: String!
  version: String!
  description: String!
  apiId: String
  id: String!
  orgHandler: String
  serviceAccessMode: String
}

" CommitInformation record"
type CommitInformation {
  "Commit hash "
  sha: String
  " Date  "
  date: String
  "Success flag  "
  success: Boolean!
  "Message  "
  message: String!
}

"DeploymentStatusResp"
type DeploymentStatusResp {
  "Id"
  id: Int!
  "Name"
  name: String!
  "Conclusion"
  conclusion: String
  "Conclusion V2 , same as conclusion but with additional warning status"
  conclusionV2: String
  "Status"
  status: String!
  "Started time"
  started_at: String
  "Completed time"
  completed_at: String
  "SHA"
  sha: String
  "Deployment is triggered automatically"
  isAutoDeploy: Boolean
  "Build was triggered at component creation"
  isTriggeredAtCreation: Boolean
  "The reason for deployment failure"
  failureReason: Int
  "commit Id of the source repo"
  sourceCommitId: String
  "Reference of the build run for Argo Workflows this will be workflow name and for GH actions this is run id."
  buildRef: String
  "Cluster Id of where the build run was ran and this is only for Argo Workflows"
  clusterId: String
  "commit or tag build"
  gitRefType: String
  "commit tag"
  commitTag: String
  "Metadata of the deployment status"
  metadata: DeploymentStatusMetadata
}

"Description."
type DeploymentStatusMetadata {
  "Summary related to governance checks"
  governance: GovernanceMetadataMapper
  "Source config type (component.yaml|endpoints.yaml|component-config.yaml)"
  sourceConfigType: String
  "Schema version of the component.yaml"
  schemaVersion: String
}

"Governance Check related Metadata related to a Build"
type GovernanceMetadataMapper {
  "Summary related to governance check rule violations"
  ruleViolationsSummary: GovernanceRuleViolationsSummary!
}

"GovernanceRuleViolationsSummary"
type GovernanceRuleViolationsSummary {
  "Error count (rules)"
  error: Int!
  "Warning count (rules)"
  warn: Int!
}

"""
Represents an Insights environment type.
"""
enum EnvironmentType {
    """
    The Choreo environments.
    """
    CHOREO

    """
    The private data-plane Choreo environments.
    """
    CHOREO_PRIVATE

    """
    The On-Premise environments.
    """
    ON_PREM
}

"""
Represents an Insights Environments.
"""
type InsightsEnvironment {
    """
    The environment ID.
    """
    id: String!

    """
    The external environment ID.
    """
    externalEnvId: String!

    """
    The internal environment ID.
    """
    internalEnvId: String

    """
    The sandbox environment ID.
    """
    sandboxEnvId: String

    """
    The environment name.
    """
    name: String!

    """
    The environment type. A Choreo environment is labeled as `CHOREO`, a private Choreo environment is labeled as
    `CHOREO_PRIVATE`, and the on-premise environment is labeled as `ON_PREM`.
    """
    type: EnvironmentType!

    """
    The environment region if available.
    """
    region: String
}

"Environment record"
type Environment {
  id: String!
  "Environment name"
  name: String!
  "Organization Uuid"
  organizationUuid: String!
  "Project Id"
  projectId: String
  "Description"
  description: String!
  "Promoted list"
  promoteFrom: [String!]
  "Organization flag"
  orgShared: Boolean!
  "Choreo environment name"
  choreoEnv: String!
  "Whether the environment is critical"
  critical: Boolean!
  apiEnvName: String!
  internalApiEnvName: String!
  externalApiEnvName: String!
  "Sandbox environment name in APIM"
  sandboxApiEnvName: String!
  namespace: String!
  vhost: String
  "Sandbox vhosts"
  sandboxVhost: String
  "Sandbox Environment id saved in APIM"
  apimSandboxEnvId: String
  "Environment id saved in APIM"
  apimEnvId: String
  "Whether the environment is migrating org level to project level"
  isMigrating: Boolean!
  "Whether the environment is pdp"
  isPdp: Boolean!
  "Environment Template id"
  templateId: String
  "Data plane id"
  dpId: String
  "Whether the environment is scaled to zero enabled"
  scaleToZeroEnabled: Boolean!
}

"WorkflowStatusDataMapper"
type WorkflowStatusDataMapper {
  "Id"
  id: Int!
  "Name"
  name: String!
  "Conclusion"
  conclusion: String
  "Status"
  status: String!
  "Started time"
  started_at: String
  "Completed time"
  completed_at: String
  "SHA"
  sha: String
}

"RerunCreateResMapper"
type RerunCreateResMapper {
  " Success flag  "
  success: Boolean!
  " Message  "
  message: String!
}

"Promote"
input Promote {
  apiVersionId: String!
  sourceReleaseId: String!
  targetReleaseId: String
  targetEnvironmentId: String
  deploymentPipelineId: String
  cronFrequency: String
  cronTimezone: String
  jobTimeoutSeconds: Int
  cronJobAllowConcurrency: Boolean
  jobRetryCount: Int
}

"Input object for update JobConfigs."
input JobConfigInput {
  "Org Handler"
  orgHandler: String!
  "Component UUID"
  componentId: String!
  "Env ID"
  environmentId: String!
  "UUID of the version"
  versionId: String!
  "Cron frequency"
  cronFrequency: String
  "Cron timezone"
  cronTimezone: String
  "Job timeout in seconds"
  jobTimeoutSeconds: Int
  "Cron allow concurrency or not"
  cronJobAllowConcurrency: Boolean
  "Maximum retry count"
  jobRetryCount: Int
}

"CommitHistory"
type CommitHistory {
  "Commit author"
  author: Author!
  "Commit message"
  message: String!
  "Git hash"
  sha: String!
  "Lates"
  isLatest: Boolean!
}

"Author"
type Author {
  "Author name"
  name: String!
  "Author email"
  email: String!
  "Commited date"
  date: String!
  "Author avatal url"
  avatarUrl: String
}

"TagHistory"
type TagHistory {
  "Tag name"
  name: String!
  "Commit hash"
  commitSha: String!
  "Tag url"
  url: String
}

"Branch"
type Branch {
  "Branch name"
  name: String!
  "Is branch protected"
  protected: Boolean!
  "ApiVersion list"
  apiVersion: [String!]!
}

type DeployedEnvironmentData {
  deploymentCount: Int!
  apiId: String
  releaseId: String!
  versionId: String!
  version: String!
  environmentId: String!
  environmentChoreoName: String!
  environmentName: String!
  build: DeployedBuild!
  deploymentStatus: String!
  cron: String!
  cronTimezone: String!
  invokeUrl: String
  configCount: Int
}

type DeployedBuild {
  buildId: String!
  deployedAt: String!
  commitHash: String!
  commit: CommitHistory
  branch: String!
  runId: String
}

type UsageStatus {
  threshold1Reached: Boolean!
  threshold2Reached: Boolean!
  currentUsage: Int!
  tierId: String!
}

type DeployedEnvironment {
  environmentId: String!
  environmentChoreoName: String!
  environmentName: String!
  deployment: DeployedInformation
}

type DeployedInformation {
  environmentId: String!
  environmentChoreoName: String!
  deploymentCount: Int!
  apiId: String
  releaseId: String!
  versionId: String!
  version: String!
  build: DeployedBuild!
  deploymentStatus: String!
  cron: String!
  cronTimezone: String!
  invokeUrl: String
  configCount: Int
}

type ComponentDeployment {
  deploymentCount: Int
  apiId: String
  imageUrl: String
  releaseId: String
  componentId: String
  environmentId: String
  versionId: String
  version: String
  build: RudderDeployedBuild
  deploymentStatus: String
  deploymentStatusV2: String
  cron: String
  cronTimezone: String
  invokeUrl: String
  configCount: Int
  scheduleLastRun: String
  lifecycleStatus: String
  apiRevision: ApimRevision
  lastTriggeredAt: String
  webAppLocalDevEnabled: Boolean
}

type RudderDeployedBuild {
  buildId: String
  deployedAt: String
  commitHash: String
  branch: String
  runId: String
  commit: Commit
  sourceConfigMigrationStatus: SourceConfigMigrationStatus
}

"Commit"
type Commit {
  "Commit author"
  author: Author!
  "Commit message"
  message: String!
  "Git hash"
  sha: String!
  "Lates"
  isLatest: Boolean!
}

type SourceConfigMigrationStatus {
  canMigrate: Boolean!
  existingFileName: String
  existingFileSchemaVersion: String
}

"ApimRevisions"
type ApimRevision {
  "Revision Name"
  displayName: String!
  "Revision id"
  id: String!
  "Revision description"
  description: String!
  "Created time"
  createdTime: Int!
  "ApimRevisionApiInfo"
  apiInfo: ApimRevisionApiInfo!
  "ApimRevisionDeploymentInfo array"
  deploymentInfo: [ApimRevisionDeploymentInfo!]!
}

"ApimRevisionApiInfo"
type ApimRevisionApiInfo {
  "Api Id"
  id: String!
}

"ApimRevisionDeploymentInfo"
type ApimRevisionDeploymentInfo {
  "Revision id"
  revisionUuid: String!
  "Environment name"
  name: String!
  "Virtual host"
  vhost: String!
  "Display name on devportal flag"
  displayOnDevportal: Boolean!
  "Deployed time"
  deployedTime: Int
  "Last deployed time"
  successDeployedTime: Int
}

type ProxyDeployment {
  apiId: String
  environment: Environment
  lifecycleStatus: String
  version: String
  apiRevision: ApimRevision
  invokeUrl: String
  deployedTime: Int
  successDeployedTime: Int
  build: ProxyBuild
  endpoint: String
  sandboxEndpoint: String
}

type ProxyBuild {
  id: String
  baseRevisionId: String
  deployedRevisionId: String
}

"InvokeData"
type InvokeData {
  "Id for the API"
  apiId: String
  "Release Id"
  releaseId: String
  "Version Id"
  versionId: String!
  "Environment Id"
  environmentId: String!
  "Environment name fron choreo"
  environmentChoreoName: String!
  "Environment name"
  environmentName: String!
  apimEnvName: String!
  lifecycleStatus: String!
  "Invoke Url"
  invokeUrl: String
  "Proxy flag"
  isProxy: Boolean
  "Version Name"
  version: String!
  apiRevision: ApimRevision
}

"""
Contains basic invoke data of the services.
This type is fetched especially for Triggers, because some of the triggers
need the invoke URL of the service, before the deployment.
"""
type BasicInvokeData {
  "ID of the environment"
  environmentId: String!
  "Environment name from Choreo (dev, prod)"
  environmentName: String!
  "Invoke url of the service"
  invokeUrl: String!
}

"Version"
type Version {
  "Version id"
  id: String!
  "Created at"
  createdAt: String!
  "Updated at"
  updatedAt: String!
  "API version"
  apiVersion: String!
  "App Id"
  appId: String!
  "Latest flag"
  latest: Boolean!
  "Proxy name"
  proxyName: String!
  "Proxy Url"
  proxyUrl: String!
  "Version status"
  state: String
}

"TriggerDeploymentResMapper"
type TriggerDeploymentResMapper {
  " Success flag  "
  success: Boolean!
  " Message  "
  message: String!
}

"Deployment record"
input Deployment {
  componentId: String!
  versionId: String!
  envId: String!
  sha: String!
  branch: String
  commitTag: String
  gitRefType: String
  cron: String
  cronTimezone: String
  shaDate: String
  jobTimeoutSeconds: Int
  jobRetryCount: Int
  cronJobAllowConcurrency: Boolean
  originCloud: String
}

"Record holds the data required to cancel a build"
input CancelBuildInput {
  "componentId - Id of the component"
  componentId: String!
  "deploymentTrackId - Id of the deployment track"
  deploymentTrackId: String!
  "runId - Id of the run"
  runId: String!
}

"EnvBuild"
type EnvBuild {
  "Environment Id"
  environmentId: String!
  "BuildRevisions"
  builds: [BuildRevisions!]!
}

"BuildRevisions"
type BuildRevisions {
  "Unique Id"
  id: String!
  "Version id"
  versionId: String!
  "Build Id"
  buildId: String!
  "Commit hash"
  commitHash: String
  "Component Id"
  componentId: String!
  "Created Date"
  createdDate: String!
  "revisions"
  revisions: [Revision!]!
}

"Revision"
type Revision {
  "Revision Environment Id"
  id: String!
  "Revision Id"
  revisionId: String!
  "Created Date"
  createdDate: String!
  "Updated Date"
  updatedDate: String!
  description: String
  "Environments"
  environments: [String!]
  "Is base revision"
  isBase: Boolean!
}

"BuildRevision"
input BuildRevision {
  buildId: String
  componentId: String!
  versionId: String!
  commitHash: String
  environmentId: String
  revisionId: String
  type: String
}

"BuildMapping"
type BuildMapping {
  "Unique Id"
  id: String!
  "Version id"
  versionId: String!
  "Build Id"
  buildId: String!
  "Commit hash"
  commitHash: String
  "Component Id"
  componentId: String!
  "Created Date"
  createdDate: String!
  "Commit Message"
  commitMessage: String
  "Revisiona array"
  revisions: [Revision!]!
}

"BuildMapper"
type BuildMapper {
  "Unique Id"
  id: String
  "Version id"
  versionId: String!
  "Build Id"
  buildId: String!
  "Commit hash"
  commitHash: String
  "Component Id"
  componentId: String!
  "Created Date"
  createdDate: String!
  "Enviornment specific revisions"
  environments: [EnvironmentRevisions!]!
  "revision specific environments"
  revisions: [RevisionWithEnvs!]!
}

"EnvironmentRevisions"
type EnvironmentRevisions {
  "Environment Id"
  id: String!
  "Revision List"
  revisions: [Revision!]!
}

"RevisionWithEnvs"
type RevisionWithEnvs {
  "Revision Environment Id"
  id: String!
  "Environment Id array"
  environments: [String!]!
  "Revision Id"
  revisionId: String!
  "Created Date"
  createdDate: String!
  "Updated Date"
  updatedDate: String!
  "Is a based revision"
  isBase: Boolean!
}

"`ObtainUserTokenResponseData` - response for exchanging oauth2 authorization code for a token"
type ObtainUserTokenResponseData {
  "flag to indicate success/failure"
  success: Boolean!
  "additional details"
  message: String!
}

"`GitHubOrgsAndRepositoriesForUser`"
type GitHubOrgsAndRepositoriesForUser {
  "GitHub organzation name"
  orgName: String!
  "Organization handler to construct the repo URL, same as the orgName for GitHub"
  orgHandler: String!
  "GitHub repository list"
  repositories: [GitHubRepositoryForUser!]!
}

"`GitHubRepositories`"
type GitHubRepositoryForUser {
  "GitHub repository name"
  name: String!
  "GitHub repository description"
  description: String
}

"`UserGitHubRepositoryValidationData`"
type UserGitHubRepositoryValidationData {
  "flag to indicate whether the repository is valid or not."
  isValid: Boolean!
  "additional message for the repository"
  message: String!
}

"SystemStatus record includes all the dependent services summarize status and individual component status."
type SystemStatus {
  "Dependent services summarize status"
  status: Status!
  "Individual service status"
  serviceStatus: [ServiceStatus!]!
}

enum Status {
  DOWN
  TROUBLE
  UP
}

"ServiceStatus holds dependent service status"
type ServiceStatus {
  "Service name"
  name: String!
  "System status message"
  message: String!
  "System status"
  status: String!
  "Is message visible in the console"
  visible: Boolean!
}

"`UserGitHubRepositoryStatusData`"
type UserGitHubRepositoryStatusData {
  "flag to indicate whether the repository is valid or not."
  isValid: Boolean!
  "html url to the repository"
  url: String!
  "additional message"
  message: String!
}

"CancelDeploymentResMapper"
type CancelDeploymentResMapper {
  " Success flag  "
  success: Boolean!
  " Message  "
  message: String!
}

"ApiVersion"
type ApiVersion {
  "API version id"
  id: String!
  "Created at timestamp"
  createdAt: String!
  "Updated at timestamp"
  updatedAt: String!
  "Organization id"
  organizationId: String!
  "Project id"
  projectId: String!
  "API version (Ex:-1.0.0)"
  apiVersion: String!
  "Branch used for the version"
  branch: String
  "VersionId used for CICD"
  versionId: String
  "Component Id"
  appId: String!
  "Is latest flag"
  latest: Boolean!
  "API accessibility"
  accessibility: String
  "Proxy name"
  proxyName: String
  "Proxy url"
  proxyUrl: String
  "Proxy id which is the APIM API id"
  proxyId: String
  "Version state"
  state: String
  "API versioning strategy for this deployment track. Values: Major, MajorMinor, LegacyMajorMinorPatch"
  versionStrategy: String
  "Optional field that store the description of this deployment track."
  description: String
  "Revision list"
  appEnvVersions: [AppEnvVersion!]
  "Auto deploy enabled or not"
  autoDeployEnabled: Boolean!
}

"RepositoryDirectory contains information about a directory within a component related repository in Choreo."
type RepositoryDirectory {
  "name of the directory"
  name: String!
}

"RepositoryBranch"
type RepositoryBranch {
  "Branch name"
  name: String!
  "Is branch protected"
  protected: Boolean
  "Mark whether the branch is default branch"
  isDefault: Boolean!
}

type AutoDeploymentStatus {
  success: Boolean!
  message: String!
}

"Description"
type RepositoryContent {
  type: String!
  "Name of the file or directory"
  name: String!
  "Path of the file or directory"
  path: String!
}

"GraphQL mutation endpoint input object for retreving repostiroy contents"
input GetRepositoryContentsEndpointInputs {
  organization: String!
  repository: String!
  branch: String!
  path: String!
}

"Config Init status"
type ConfigInitStatus {
  " Success flag  "
  success: Boolean!
  " Message  "
  message: String!
}

"AutoDeploymentResMapper"
type AutoDeploymentResMapper {
  " Success flag"
  success: Boolean!
  " Message"
  message: String!
  "Auto Deployment data"
  data: AutoDeploymentData!
}

"AutoDeploymentData"
type AutoDeploymentData {
  "Auto Build ID"
  autoBuildId: String!
  "Auto Build enabled flag"
  autoBuildEnabled: Boolean!
  "Component ID"
  componentId: String!
  "Version ID"
  versionId: String!
  "Component ID"
  envId: String!
}

"GraphQL mutation input object for updating buildpack build config"
input UpdateBuildpackBuildConfigInput {
  "componentId - UUID of the component"
  componentId: String!
  "versionId - UUID of the version"
  versionId: String!
  "buildContext - Build context"
  buildContext: String
  "languageVersion - Language version"
  languageVersion: String
  "buildpackId - Buildpack id"
  buildpackId: String
  "environmentVariables - Buildpack runtime environment variables"
  environmentVariables: [BuildpackBuildEnvironmentVariable!]
  "isUnitTestEnabled - Flag to enable unit tests"
  isUnitTestEnabled: Boolean
  "pullLatestSubmodules - Flag to enable always pull latest submodules"
  pullLatestSubmodules: Boolean
  "enableTrivyScan - Flag to enable container trivy scan"
  enableTrivyScan: Boolean
  "runCommand - run command"
  runCommand: String
}

"Represent the Buildpack Build environment variable key-value pair"
input BuildpackBuildEnvironmentVariable {
  "id - Id of the environment variable"
  id: String
  "key - Key of the environment variable"
  key: String!
  "value - Value of the environment variable"
  value: String!
}

"Represents an update web app config request."
input UpdateWebAppConfigInput {
  componentId: String!
  outputDirectory: String!
  buildCommand: String
  runtimeVersion: String
  dockerContext: String
  enableTrivyScan: Boolean
}

type ScanResult {
  checkovScan: String
  trivyScan: String
}

type BuildLogs {
  integrationProjectBuild: String
  unitTest: String!
  libraryTrivyReport: String
  dropinsTrivyReport: String
  mainSequenceValidation: String
  mIVersionValidation: String
  postBuildCheckLogs: String
  proxyBuildLogs: String
  governanceLogs: String!
  configValidationLogs: String!
}

type RepositoryMetaDataMapperData {
  isBareRepo: Boolean!
  isSubPathValid: Boolean!
  isValidRepo: Boolean!
  isSubPathEmpty: Boolean!
  hasBallerinaTomlInPath: Boolean!
  hasBallerinaTomlInRoot: Boolean!
  hasDockerfileInPath: Boolean!
  isDockerfilePathValid: Boolean!
  isDockerContextPathValid: Boolean!
  hasOpenApiFileInPath: Boolean!
  hasPomXmlInPath: Boolean!
  hasPomXmlInRoot: Boolean!
  isOpenApiFilePathValid: Boolean!
  isLibPathValid: Boolean!
  isBuildpackPathValid: Boolean!
  buildpackPath: String!
  isProcfileExists: Boolean!
  isTestRunnerPathValid: Boolean!
  isEndpointYamlExists: Boolean!
}

"ComponentWebhook"
type ComponentWebhook {
  "Webhook count"
  webhookCount: Int!
  "Has code push webhook"
  hasCodePushWebhook: Boolean!
  "Is limit reached"
  isLimitReached: Boolean!
  "Has webhook create access"
  hasWebhookCreateAccess: Boolean!
  "Has org level webhook"
  hasOrgLevelCodePushWebhook: Boolean!
}

"DistinctComponentCount record"
type DistinctComponentCount {
  "Component type"
  componentType: String!
  "Component count"
  count: Int!
}

"Component Endpoint object that is exposed from the GraphQL"
type ComponentEndpoint {
  "Unique UUID of the endpoint object"
  id: String!
  "Created date for the endpoint"
  createdAt: String!
  "Updated date for the endpoint"
  updatedAt: String!
  "UUID of the release where this specific endpoint is attached"
  releaseId: String!
  "Numeric port value that gets exposed via this endpoint"
  port: Int!
  "UUID of the environment this endpoint belong to"
  environmentId: String!
  "Display name of the endpoint"
  displayName: String!
  "Name of the endpoint"
  name: String!
  "Invoke url of this specific endpoint. # DEPRECATED - Use 'project_url' field instead."
  invokeUrl: String!
  "Hostname of the endpoint. # DEPRECATED - Use one of 'project_url', 'organization_url','public_url' to derive the hostname."
  hostName: String!
  "Communication protocol this endpoint is accepting. Example: HTTP, gRPC, GraphQL. # DEPRECATED - Use 'type' field instead."
  protocol: String!
  "Optional field that describe the API context that exposed via this endpoint."
  apiContext: String
  "Optional field that provides the path to the API definition."
  apiDefinitionPath: String
  """
  Optional field that indicates the network level visibility of this endpoint. 
  Accepted values: Project|Organization|Public. Defaults to Public. # DEPRECATED - Use 'networkVisibilities' field instead.
  """
  visibility: String
  """
  Optional field that indicates the network level visibilities of this endpoint.
  Accepted values: Combination of Project|Organization|Public. Defaults to Public.
  """
  networkVisibilities: [String!]
  type: String!
  "A field that indicates the managed API id given from the API Manager."
  apimId: String!
  "Revision id of the managed API endpoint which is deployed to a Gateway. # DEPRECATED - Use 'internalGwRevisionId' and 'externalGwRevisionId' fields instead."
  apimRevisionId: String!
  "Revision id of the managed API endpoint which is deployed to the internal gateway."
  internalGwRevisionId: String
  "Revision id of the managed API endpoint which is deployed to the external gateway."
  externalGwRevisionId: String
  "A unique API name geenrated for this specific endpoint."
  apimName: String!
  "Indicates the unmanaged endpoint url which can be directly accessible within a project."
  projectUrl: String!
  "Indicates the managed API endpoint url that can be only accessible within the organization. The value will be empty if the endpoint is not exposed."
  organizationUrl: String!
  "Indicates the managed API endpoint url that is exposed to the internet. The value will be empty if the endpoint is not exposed."
  publicUrl: String!
  "Indicates the default managed API endpoint url that is exposed to the internet. The value will be empty if the endpoint is not exposed."
  defaultPublicUrl: String
  "Indicates the default managed API endpoint url that can be only accessible within the organization. The value will be empty if the endpoint is not exposed."
  defaultOrganizationUrl: String
  "Indicates whether this endpoint is auto generated using Ballerina source code."
  isAutoGenerated: Boolean!
  "A field that indicates the endpoint deployment status."
  state: String!
  "Indicates the reason for why this endpoint in the current state."
  stateReason: ComponentEndpointStateReason
  "a unique string to identify the endpoint."
  signature: String
  """
  generated name of the configuration group for this specific endpoint. 
  This is used to find the API configuration from the configuration-service when deploying.
  """
  configurationGroupName: String
  "Indicates the soft delete state for this endpoint."
  isDeleted: Boolean!
  "Indicates the time of this endpoint is deleted."
  deletedAt: String!
  "Indicates whether this endpoint has the short URL."
  hasShortUrl: Boolean!
  "Indicates the state of the short URL reassignment."
  shortUrlReassignState: String!
  "Indicates whether the scope has been added to the endpoint via choreo console"
  isScopeAdded: Boolean!
  "Indicates the source of the generated endpoint"
  generationSource: String
}

"""
Component Endpoint State Reason object that is exposed from the GraphQL which indicates 
current state of the endpoint.
"""
type ComponentEndpointStateReason {
  "Code indicates machine-readable value indicating the current state."
  code: String!
  "Indicates a human-readable message for the current state."
  message: String!
  "Indicates an optional informative cause for the state for troubleshooting purpose."
  details: String!
  "Indicates the id of the worker that reported this state."
  workerId: String!
}

"GraphQL mutation input object for creating endpoints"
input CreateComponentEndpointInput {
  componentId: String!
  versionId: String!
  releaseId: String!
  port: Int!
  protocol: String
  type: String
  displayName: String!
  apiContext: String
  apiDefinitionPath: String
  visibility: String
  networkVisibilities: [String!]
}

"GraphQL mutation input object for updating endpoints"
input UpdateComponentEndpointInput {
  componentId: String!
  versionId: String!
  releaseId: String!
  endpointId: String!
  displayName: String
  apiContext: String
  apiDefinitionPath: String
  apiName: String
  visibility: String
  networkVisibilities: [String!]
}

"GraphQL mutation input object for deleting endpoints"
input DeleteComponentEndpointInput {
  componentId: String!
  versionId: String!
  releaseId: String!
  endpointId: String!
}

"GraphQL query input object for retrieving an endpoint"
input QueryComponentEndpointInput {
  componentId: String!
  versionId: String!
  releaseId: String!
  endpointId: String!
}

"GraphQL query input object for retrieving endpoints attach to a release"
input QueryComponentEndpointsInput {
  componentId: String!
  versionId: String!
  options: QueryComponentEndpointOptions
}

"QueryComponentEndpointOptions for quering endpoint results"
input QueryComponentEndpointOptions {
  filter: QueryComponentEndpointFilterOptions
}

"QueryComponentEndpointFilterOptions for filtering endpoint data"
input QueryComponentEndpointFilterOptions {
  releaseIds: [String!]
}

"Component Endpoint Api Definition object that is exposed from the GraphQL"
type ComponentEndpointApiDefinition {
  "Base64 encoded api definition"
  content: String!
}

"GraphQL query input object for retrieving an endpoint api definition"
input QueryComponentEndpointApiDefinitionByEndpointNameAndCommitHashInput {
  componentId: String!
  versionId: String!
  commitHash: String!
  endpointName: String!
  releaseId: String!
  endpointHash: String!
}

"GraphQL query input object for retrieving an endpoint api definition"
input QueryComponentEndpointApiDefinitionInput {
  componentId: String!
  versionId: String!
  endpointId: String!
  commitHash: String
}

"GraphQL mutation input object for promoting endpoints"
input PromoteComponentEndpointInput {
  componentId: String!
  versionId: String!
  sourceReleaseId: String!
  targetEnvironmentId: String!
}

"GraphQL mutation input object for generating endpoints"
input GenerateComponentEndpointInput {
  componentId: String!
  versionId: String!
  releaseId: String!
  commitHash: String!
  dryRun: Boolean
}

"GraphQL query input object for reassigning short url for a release"
input ReassignEndpointShortUrlInput {
  componentId: String!
  versionId: String!
  targetEndpointId: String!
  releaseId: String!
}

"GraphQL mutation input object for deploying images"
input DeployImageInput {
  componentId: String!
  releaseId: String!
  imageName: String
  imageTag: String
  imageRepoName: String
  imageUrl: String
  imageId: String
  cron: String
  cronTimezone: String
  "Job timeout in seconds"
  jobTimeoutSeconds: Int
  "Cron allow concurrency or not"
  cronJobAllowConcurrency: Boolean
  jobRetryCount: Int
  apiSettings: String
}

type CommonCredentialService {
  id: String!
  createdAt: String!
  name: String!
  organizationId: Int!
  organizationUuid: String!
  projectId: String
  type: String!
  scope: String!
  referenceToken: String!
  metadata: String!
  serverUrl: String
}

"Represents a create common credential request."
input CreateCommonCredentialInput {
  name: String!
  type: String!
  projectId: String
  orgUuid: String!
  bitbucketCredential: BitbucketAppPasswordType
  bitbucketServerConfig: BitbucketServerConfigType
  gitPatCredential: GitPatType
  gitLabServerConfig: GitLabServerConfigType
  swaggerHubConfig: SwaggerHubConfigType
}

"Represents Bitbucket app password type token."
input BitbucketAppPasswordType {
  appPassword: String!
  userName: String!
}

"Represents Bitbucket server config."
input BitbucketServerConfigType {
  serverUrl: String!
  serverToken: String!
}

"Represents a create git credential request."
input GitPatType {
  pat: String!
}

"Represents GitLab Server config."
input GitLabServerConfigType {
  ""
  serverUrl: String!
  ""
  pat: String!
}

"Represents a SwaggerHub API-key config"
input SwaggerHubConfigType {
  apiKey: String!
}

"DeleteCommonCredential"
type DeleteCommonCredentialResp {
  "deleted message"
  message: String!
}

type CheckDeleteGitCredentialResp {
  canDelete: Boolean!
  components: [GitCredentialInUseComponentInfo!]
}

type GitCredentialInUseComponentInfo {
  projectName: String!
  componentNames: [String!]!
}

"Update a common credential request."
input UpdateCommonCredentialInput {
  name: String
  type: String!
  projectId: String
  orgUuid: String!
  bitbucketCredential: BitbucketAppPasswordType
  bitbucketServerConfig: BitbucketServerConfigType
  gitPatCredential: GitPatType
  gitLabServerConfig: GitLabServerConfigType
  swaggerHubConfig: SwaggerHubConfigType
}

"GitPermissions record"
type GitPermissions {
  "Has account read permission"
  accountRead: Boolean!
  "Has PR write permission"
  prWrite: Boolean!
  "Has webhook write permission"
  webhookWrite: Boolean!
  "Has organizations"
  hasOrgs: Boolean!
  "Has repositories"
  hasRepos: Boolean!
  "Is valid credential"
  validCredential: Boolean!
}

"GraphQL query input object for git token permissions"
input QueryGitTokenPermissions {
  secretRef: String
  bitbucketToken: BitbucketToken
  bitbucketServerConfig: BitbucketServerConfig
  gitLabServerConfig: GitLabServerConfig
}

"Bitbucket token object that is exposed from the GraphQL"
input BitbucketToken {
  username: String!
  appPassword: String!
}

"Bitbucket server configuration."
input BitbucketServerConfig {
  serverUrl: String!
  serverToken: String!
}

"GitLab Server configuration."
input GitLabServerConfig {
  ""
  serverUrl: String!
  ""
  pat: String!
}

"GraphQL mutation input object for creating deployment tracks"
input CreateDeploymentTrackInput {
  orgUuid: String!
  componentId: String!
  apiVersion: String
  branch: String
  description: String
}

"GraphQL mutation input object for updating deployment tracks"
input UpdateDeploymentTrackInput {
  id: String!
  componentId: String!
  branch: String
  description: String
  enableAutoDeploy: Boolean
}

"Deployment Track Image object that is exposed from the GraphQL"
type DeploymentTrackImage {
  "UUID of the image object."
  imageId: String!
  "Created date for the image"
  createdAt: String!
  "Updated date for the image"
  updatedAt: String!
  "latest build completion time"
  builtAt: String!
  "Commit hash of the image"
  commitHash: String
  "Commit message of the commit used to build the image"
  commitMessage: String
  "author details of the commit used to build the image"
  author: CommitAuthor
  "latest run id of the image"
  runId: String!
}

type CommitAuthor {
  name: String!
  email: String!
  date: String!
  avatarUrl: String
}

"GraphQL get deployment track images input object"
input GetDeploymentTrackImagesInput {
  componentId: String!
  id: String!
}

"GraphQL mutation input object for deploying deployment tracks"
input DeployDeploymentTrackInput {
  id: String!
  componentId: String!
  imageId: String!
  environmentId: String!
  cron: String
  cronTimezone: String
  shaDate: String
  jobTimeoutSeconds: Int
  cronJobAllowConcurrency: Boolean
  jobRetryCount: Int
  proxyConfig: ProxyConfig
  apiSettings: String
  deploymentPipelineId: String
}

"Proxy Specific Configurations"
input ProxyConfig {
  keys: ProxyTargetEndpoint
  accessMode: String
}

"ProxyTargetEndpoint contains the endpoint information for a proxy."
input ProxyTargetEndpoint {
  productionEndpoint: String!
  sandboxEndpointChoreo: String
}

"DeleteDeploymentTrackRes"
type DeleteDeploymentTrackRes {
  "Whether the deployment track can be deleted"
  canDelete: Boolean!
  "Deleted status"
  status: String!
  "Human readable message indicating the reason for any failures"
  message: String!
  "Encoded dynamic data coming from the delete manager"
  encodedData: String
}

"GraphQL mutation input object for deleting deployment tracks"
input DeleteDeploymentTrackInput {
  orgHandler: String!
  componentId: String!
  projectId: String!
  deploymentTrackId: String!
  skipValidation: Boolean
}

"CheckDeploymentTrackDeletableRes"
type CheckDeploymentTrackDeletableRes {
  "Whether the deployment track can be deleted"
  canDelete: Boolean!
  "Human readable message indicating the reason for any failures"
  message: String!
}

"Migrated Source Configuration Content object that is exposed from the GraphQL"
type MigratedSourceConfigurationContent {
  "base64 encoded file content"
  fileContent: String!
  "file name"
  fileName: String!
  "source file from which the content is migrated"
  migratedFrom: String!
}

"GraphQL query input object for retrieving migrated source configuration content"
input GetMigratedSourceConfigurationContentInput {
  componentId: String!
  deploymentTrackId: String!
  commitHash: String
}

"Public type for the component path record"
type ComponentPath {
  "The name of the component"
  name: String!
  "The build preset of the component"
  preset: String!
  "The path of the component"
  path: String!
  "The Docker context path (for 'Dockerfile' preset)"
  dockerContext: String
  dockerfilePath: String
  otherPresets: [String!]
}

"Represents an update test runner config request."
input UpdateTestRunnerConfigInput {
  componentId: String!
  postmanDirectory: String!
}

"Execution object exposed from the GraphQL"
type Execution {
  "ID"
  id: String!
  "Created at date"
  createdAt: String!
  "Updated at date"
  updatedAt: String!
  "Argument name"
  arguments: [ExecutionArgument!]!
  "Execution status; one of SUCCESS, PENDING, FAILED"
  status: ExecutionStatus!
  "Release ID"
  releaseId: String!
  "Job name"
  jobName: String!
}

"ExecutionArgument object exposed from the GraphQL."
type ExecutionArgument {
  "Argument name"
  argumentName: String!
  "Argument value"
  argumentValue: String!
}

"ExecutionStatus object exposed from the GraphQL."
enum ExecutionStatus {
  TERMINATED
  FAILED
  CREATED
  PENDING
}

"GraphQL query input object for retrieving an executions by ID"
input QueryExecutionInput {
  componentId: String!
  releaseId: String!
  id: String!
}

"GraphQL query input object for retrieving an executions"
input QueryExecutionsInput {
  componentId: String!
  releaseId: String!
  status: ExecutionStatus
}

"ExecutionConfigs object exposed from the GraphQL"
type ExecutionConfigs {
  cronjobFrequency: String!
  cronjobTimezone: String!
  cronjobAllowConcurrency: Boolean
  timeoutSeconds: Int
  retryCount: Int
}

"GraphQL mutation input object for terminating an execution"
input TerminateExecutionInput {
  componentId: String!
  releaseId: String!
  controllerId: String!
}

"Represents user defined endpoint"
type UserProvidedEndpoint {
  "Unique UUID of the endpoint object"
  id: String!
  "Component ID"
  componentId: String!
  "Deployment track ID"
  deploymentTrackId: String!
  "Name of the endpoint"
  name: String!
  "Display name of the endpoint"
  displayName: String!
  "Port of the endpoint"
  port: Int!
  "Type of the endpoint ex: REST, GraphQL, GRPC"
  type: String!
  "Base path of the endpoint"
  basePath: String!
  "Schema file path of the api spec"
  schemaFilePath: String!
  "Network visibilities of the endpoint"
  networkVisibilities: [String!]!
}

input UpdateUserProvidedEndpointInput {
  id: String!
  componentId: String!
  deploymentTrackId: String!
  name: String
  displayName: String
  port: Int
  basePath: String
  schemaFilePath: String
  networkVisibilities: [String!]
}

"Represents organization governance policies"
type organizationGovernancePolicies {
  "Organization ID"
  organizationId: String!
  "Indicates whether Trivy scan is enforced for the organization"
  isTrivyScanEnforced: Boolean!
  "Secret access policy for the organization"
  secretAccessPolicy: secretAccessPolicy!
}

"Represents secret access policy for the organization"
type secretAccessPolicy {
  "List of environment templates where secret access is enabled"
  envTemplates: [EnvTemplate!]!
}

"Represents an environment template in cloud maanger."
type EnvTemplate {
  "UUID of the environment template"
  id: String!
  "Created timestamp of the environment template"
  created_at: String!
  "Organization (int) ID of the environment template"
  organization_id: Int!
  "Organization UUID of the environment template"
  organization_uuid: String
  "Name of the environment template"
  env_name: String!
  "Region of the environment template"
  region: String!
  "Choreo environment(dev, prod, private_dp) of the environment template"
  choreo_env: String!
  "Cluster ID of the environment template"
  cluster_id: String!
  "Docker credential UUID of the environment template"
  docker_credential_uuid: String!
  "External APIM environment name of the environment template"
  external_apim_env_name: String!
  "Internal APIM environment name of the environment template"
  internal_apim_env_name: String!
  "Sandbox APIM environment name of the environment template"
  sandbox_apim_env_name: String!
  "Promote from environment order"
  promote_from: [String!]
  "Migrate from environment"
  migrate_from: String
  "specifies whether a production environment or not"
  critical: Boolean!
  "PDP web app DNS prefix"
  pdp_web_app_dns_prefix: String
  "DNS prefix"
  dns_prefix: String
  "Specifies whether the environment template secrets can be read or not"
  can_read_secrets: Boolean!
}

"OrganizationSettings"
type OrganizationSettings {
  "organization uuid"
  organizationId: String!
  "Trivy scan enforcement status"
  isTrivyScanEnforced: Boolean!
}

"UpdateOrganizationSettingsInput"
input UpdateOrganizationSettingsInput {
  isTrivyScanEnforced: Boolean!
}

"Represents the response of updating the organization secret access policy."
type UpdateOrganizationSecretAccessPolicyResponse {
  "Message indicating the result of the update operation"
  message: String!
}

"Represents the input for updating the organization secret access policy."
input UpdateOrganizationSecretAccessPolicyInput {
  enabledEnvTemplateIds: [String!]!
}

"Component data holds all the infomation related to platformer and choreo components"
type ComponentData {
  "Name of the component"
  name: String!
  apiName: String
  "Component Handler"
  handler: String
  "Organization id"
  orgId: Int!
  "Organization handler"
  orgHandler: String!
  "Display name"
  displayName: String!
  "Display type"
  displayType: String!
  "Project id"
  projectId: String!
  "Labels to be identify the component"
  labels: String!
  canDelete: Boolean
  deleteReason: String
  ownerName: String
  ownerId: Int
  "Version of the component"
  version: String!
  "Description of the component"
  description: String!
  "Ballerina Version"
  ballerinaVersion: String
  "Ballerina Template"
  ballerinaTemplate: String
  "APIM API id"
  apiId: String
  "Component id"
  id: String
  "Organization uuid"
  orgUuid: String
  updatedAt: String
  createdAt: String
  "Last build date of the component"
  lastBuildDate: String
  triggerID: Int
  triggerChannels: String
  sampleTemplate: String
  "Transport layer of the component is http or not"
  httpBased: Boolean
  apiVersion: [ApiVersion!]
  versions: [ProxyVersion!]
  "git repository url for application source code"
  srcGitRepoUrl: String
  "Component initial version API is 'internal' or 'external'"
  accessibility: String
  "sub path of the repository to be used for the component source."
  repositorySubPath: String
  """
  type of the repository to denote whether this is a Choreo managed, user managed & empty \
  or user managed & non-empty repository.
  """
  repositoryType: String
  "branch of the given git repository, used to connect user managed and non-empty repositories."
  repositoryBranch: String
  "This will return whether the component migration is completed."
  isMigrationCompleted: Boolean
  """
  this will provide whther user needs to start with a ballerina temaplate for \
  the given display type
  """
  initializeAsBallerinaProject: Boolean
  "this will provide whether user needs to enable cell diagram for the given component"
  enableCellDiagram: Boolean
  secretRef: String
  repository: RepositoryResponseDataMapper
  "this will provide whether the component is a mono repo component"
  isMonoRepoComponent: Boolean
  "Status of the component"
  status: ComponentStatus!
  "Status of the component creation - temporary field until RIS"
  createStatus: String
  "If the component creation is async"
  isAsyncCreation: Boolean
  skipDeploy: Boolean
  isUnifiedConfigMapping: Boolean
  isPublicRepo: Boolean
  endpointShortUrlEnabled: Boolean
  serviceAccessMode: String
  componentSubType: String
  originCloud: String
  endpointUrl: String
  apiContext: String
  apiAccessibility: String
  apiDirectoryPath: String
  "To enable auto deploy at the time of component creation"
  enableAutoDeploy: Boolean
  enableAutoBuild: Boolean
  securityScheme: [String!]
  isSystemComponent: String
}

"Version"
type ProxyVersion {
  "Version name"
  version: String!
  "Api Id"
  apiId: String!
  "Is default flag"
  isDefault: Boolean!
  "Created date"
  createdDate: String!
  "gitHub repository branch : optional"
  branch: String
  "version Id"
  versionId: String
}

"Status of the Component"
enum ComponentStatus {
  NEW_COMPLETED
  FAILED
  PARTIALLY\-SUCCESSFUL
  SUCCESSFUL
  IN\-PROGRESS
  INITIATED
}

"ProxyComponentCreationSchema"
input ProxyCreateComponentSchema {
  name: String!
  orgId: Int!
  orgHandler: String!
  projectId: String!
  labels: String!
  description: String!
  componentType: String!
  proxyConfig: ProxySourceRepoConfig!
  accessibility: String
  displayName: String!
  version: String!
  context: String!
  endpointUrl: String!
  originCloud: String
  isPublicRepo: Boolean
  secretRef: String
  componentSubType: String
}

"ProxySourceRepoConfig"
input ProxySourceRepoConfig {
  sourceRepoUrl: String
  sourceRepoBranch: String
  apiDirectory: String
}

"CodeServerInput record holds the data required to create a code-server."
input CodeServerInput {
  userId: String!
  organizationId: String!
  projectId: String!
  componentId: String
  orgHandle: String!
  registryId: String!
  imageUrl: String!
  sourceCommitHash: String
  deploymentTrackId: String
}

"GitToken record"
type GitToken {
  "Git token"
  token: String!
  "Git organization"
  gitOrganization: String!
  "Git repository"
  gitRepository: String!
}

"GitTokenRequest record"
input GitTokenRequest {
  gitOrg: String!
  gitRepo: String!
}
